import { Card, Layout, Tabs } from "antd";
import { Content } from "antd/lib/layout/layout";
import { useEffect, useMemo, useState } from "react";
import ClientTab from "../../components/CockpitTabs/Client";
import FavoriteTab from "../../components/CockpitTabs/Favorite";
import ProfissionalTab from "../../components/CockpitTabs/Profissional";
import WalletTab from "../../components/CockpitTabs/Wallet";
import AverageConsumption from "../../components/Graphics/AverageConsumption";
import ContractsPerWallet from "../../components/Graphics/ContractsPerWallet";
import ExpirationOfContracts from "../../components/Graphics/ExpirationOfContracts";
import ExpireHours from "../../components/Graphics/ExpireHours";
import HoursLimitGraph from "../../components/Graphics/HoursLimitGraph";
import HoursPerProfissional from "../../components/Graphics/HoursPerProfissional";
import NewContracts from "../../components/Graphics/NewContracts";
import OpenTickets from "../../components/Graphics/OpenTickets";
import TicketsOfWalletManages from "../../components/Graphics/TicketsOfWalletManages";
import TicketsPerProfissional from "../../components/Graphics/TicketsPerProfissional";
import ReservedHours from "../../components/Graphics/ReservedHours";
import ConsumedHoursPerMonth from "../../components/Graphics/ConsumedHoursPerMonth";
import UnopenedTicketsGraph from "../../components/Graphics/UnopenedTickets";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import {
  dynamoGet,
  dynamoGetById,
  useDynamoGet,
} from "../../service/apiDsmDynamo";
import { otrsGet } from "../../service/apiOtrs";
import { formatContracts } from "../../controllers/cockpit/cockpitController";
import {
  clientComponents,
  profissionalComponents,
  walletComponents,
} from "./constants/cockpitComponentsList";
import { shallowEqual, useSelector } from "react-redux";
import { getCustomerByParameter } from "../../controllers/clients/clientsController";
import { setActiveCustomersWithActiveContractState } from "../../store/actions/customers-action";
import useSWR from "swr";

export const Cockpit = (props) => {
  const activeCustomersWithActiveContract = useSelector(
    (state) => state.customers.activeCustomersWithActiveContract,
    shallowEqual
  );

  const [collapsed, setCollapsed] = useState(false);
  const [tableTicketModal, setTableTicketModal] = useState(false);
  const [donutTableVisible, setDonutTableVisible] = useState(false);
  const [kanbanModalVisible, setKanbanModalVisible] = useState(false);
  const [consumptionModalVisible, setConsumptionModalVisible] = useState(false);
  const [tableExpireModal, setTableExpireModal] = useState(false);
  const [allQueues, setAllQueues] = useState([]);
  const [allContracts, setAllContracts] = useState(false);
  const [allWallets, setAllWallets] = useState(false);
  const [allClients, setAllClients] = useState(false);
  const [contractTypes, setContractTypes] = useState(false);
  const [allProfessionals, setAllProfessionals] = useState(false);
  const [contractsFromCustomer, setContractsFromCustomer] = useState();
  const permissions = useSWR("cockpit", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      return [...data.permissions.find((x) => x.page === "Cockpit SDM").actions];
    } catch (error) {
      console.log('⚠️ Cockpit: Erro ao buscar permissões, usando fallback:', error.message);
      // Fallback com TODOS os códigos de abas necessários
      return [
        { code: "view_favorites_tab" },
        { code: "view_wallet_tab" },
        { code: "view_professionals_tab" },
        { code: "view_customers_tab" },
        { code: "view_tickets_tab" },
        { code: "view_wallets_tab" },
        { code: "view_cockpit_data" },
        { code: "view_charts" },
        { code: "view_statistics" }
      ];
    }
  });
  const dsmContracts = useDynamoGet(
    `${process.env.REACT_APP_STAGE}-contracts`
  )?.data;

  const [favorites, setFavorites] = useState([]);

  const getActiveCustomersWithActiveContract = async () => {
    const route = "customers/read/hasActiveContracts";
    const customers = await getCustomerByParameter(route, { status: 1 });
    setActiveCustomersWithActiveContractState(customers);
  };

  useEffect(() => {
    getAllWallets();
    getFavoriteFromStorage();
    getContractTypes();
    getAllUsers();
    getAllClients();
    getAllQueues();
    getActiveCustomersWithActiveContract();
  }, []);

  useEffect(() => {
    const contracts = formatContracts(dsmContracts);
    setAllContracts(contracts);
  }, [dsmContracts]);

  const isDisable = useMemo(() => {
    if (
      allWallets.length > 0 &&
      allClients.length > 0 &&
      allContracts.length > 0 &&
      allProfessionals.length > 0 &&
      allQueues.length > 0
    ) {
      return false;
    } else {
      return true;
    }
  }, [allWallets, allClients, allContracts, allProfessionals, allQueues]);

  function showTableTicketModal() {
    setTableTicketModal(!tableTicketModal);
  }

  function showTableConsumption() {
    setConsumptionModalVisible(!consumptionModalVisible);
  }

  function showDonutTableModal() {
    setDonutTableVisible(!donutTableVisible);
  }

  function showKanbanModal() {
    setKanbanModalVisible(!kanbanModalVisible);
  }

  function showTableExpireModal() {
    setTableExpireModal(!tableExpireModal);
  }

  async function getAllQueues() {
    const { data } = await otrsGet("read/ticket/queues/all/0");
    setAllQueues(data);
  }

  async function getAllClients() {
    const { data } = await otrsGet("read/customer/all/0");
    setAllClients(Object.values(data).filter((client) => client.valid_id === 1));
  }

  async function getAllWallets() {
    const allWallets = await dynamoGet(
      `${process.env.REACT_APP_STAGE}-wallets`
    );
    setAllWallets(allWallets);
  }

  async function getAllUsers() {
    const { data } = await otrsGet("read/user/all/0");
    setAllProfessionals(data);
  }

  function getContractTypes() {
    dynamoGet(`${process.env.REACT_APP_STAGE}-contract-type`).then((resp) => {
      setContractTypes(resp);
    });
  }

  function getFavoriteFromStorage() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      setFavorites(localStorageData);
    }
  }

  function setVisionFavorite(visionName) {
    let newFavorites = structuredClone(favorites);
    if (
      newFavorites.filter((val) => val.component === visionName).length === 0
    ) {
      newFavorites.push({
        component: visionName,
        name: components[visionName].name,
      });
    } else {
      let index = newFavorites.findIndex(
        (value) => value.component === visionName
      );
      if (index !== -1) {
        newFavorites.splice(index, 1);
      }
    }
    setFavorites(newFavorites);
    localStorage.setItem("favorites", JSON.stringify(newFavorites));
  }

  const components = {
    HoursLimitGraph: {
      name: "Limite diario de horas",
      page: "profissional",
      object: [
        <HoursLimitGraph
          setVisionFavorite={setVisionFavorite}
          users={allProfessionals}
          favorites={favorites}
          idNonBillable="primary"
          idBillable="primary"
        />,
      ],
    },
    HoursPerProfissional: {
      name: "Horas por mes",
      page: "profissional",
      object: [
        <HoursPerProfissional
          setVisionFavorite={setVisionFavorite}
          clients={activeCustomersWithActiveContract}
          contracts={allContracts}
          favorites={favorites}
          contractsFromCustomer={contractsFromCustomer}
          setContractsFromCustomer={(value) => setContractsFromCustomer(value)}
        />,
      ],
    },
    ExpirationOfContracts: {
      name: "Vencimento por contrato",
      page: "wallet",
      object: [
        <ExpirationOfContracts
          contractTypes={contractTypes}
          professionals={allProfessionals}
          favorites={favorites}
          isModal={true}
          wallets={allWallets}
          contracts={allContracts}
          showDonutTableModal={showDonutTableModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    OpenTickets: {
      name: "Tickets abertos",
      page: "wallet",
      object: [
        <OpenTickets
          favorites={favorites}
          isModal={true}
          allCustomers={activeCustomersWithActiveContract}
          showKanbanModal={showKanbanModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    TicketsPerProfissional: {
      name: "Ticket por profissional",
      page: "profissional",
      object: [
        <TicketsPerProfissional
          professionals={allProfessionals}
          wallets={allWallets}
          queues={allQueues}
          contracts={allContracts}
          isModal={true}
          favorites={favorites}
          showTableTicketModal={showTableTicketModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    UnopenedTicketsGraph: {
      name: "Sem abrir tickets",
      page: "client",
      object: [
        <UnopenedTicketsGraph
          contractType={contractTypes}
          contracts={allContracts}
          clients={activeCustomersWithActiveContract}
          favorites={favorites}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    AverageConsumption: {
      name: "Media de consumo",
      page: "client",
      object: [
        <AverageConsumption
          contracts={allContracts}
          isModal={true}
          clients={activeCustomersWithActiveContract}
          favorites={favorites}
          showTableConsumption={showTableConsumption}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    NewContracts: {
      name: "Novos contratos",
      page: "client",
      object: [
        <NewContracts
          favorites={favorites}
          contractTypes={contractTypes}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    ContractsPerWallet: {
      name: "Contratos por carteira",
      page: "wallet",
      object: [
        <ContractsPerWallet
          wallets={allWallets}
          contracts={allContracts}
          favorites={favorites}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    ExpireHours: {
      name: "Horas a expirar e expiradas",
      page: "client",
      object: [
        <ExpireHours
          isModal={true}
          favorites={favorites}
          showTableExpireModal={showTableExpireModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    ReservedHours: {
      name: "Horas reservadas",
      page: "client",
      object: [
        <ReservedHours
          isModal={true}
          favorites={favorites}
          allClients={activeCustomersWithActiveContract}
          allWallets={allWallets}
          allContracts={allContracts}
          showTableExpireModal={showTableExpireModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    ConsumedHoursPerMonth: {
      name: "Horas consumidas",
      page: "client",
      object: [
        <ConsumedHoursPerMonth
          isModal={true}
          allClients={activeCustomersWithActiveContract}
          allWallets={allWallets}
          allContracts={allContracts}
          favorites={favorites}
          showTableExpireModal={showTableExpireModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
    TicketWalletManagement: {
      name: "Gerenciamento de tickets por contrato",
      page: "wallet",
      object: [
        <TicketsOfWalletManages
          isModal={true}
          allClients={activeCustomersWithActiveContract}
          favorites={favorites}
          showTableExpireModal={showTableExpireModal}
          setVisionFavorite={setVisionFavorite}
        />,
      ],
    },
  };

  const allComponents = [
    {
      key: 0,
      label: "Favoritos",
      permissionCode: "view_favorites_tab",
      disabled: isDisable,
      children: <FavoriteTab components={components} favorites={favorites} />,
    },
    {
      key: 1,
      label: "Carteira",
      permissionCode: "view_wallet_tab",
      children: (
        <WalletTab
          favorites={favorites}
          walletComponents={walletComponents}
          components={components}
          setVisionFavorite={setVisionFavorite}
        />
      ),
    },
    {
      key: 2,
      label: "Profissional",
      permissionCode: "view_professionals_tab",
      disabled: isDisable,
      children: (
        <ProfissionalTab
          profissionalComponents={profissionalComponents}
          components={components}
          favorites={favorites}
          setVisionFavorite={setVisionFavorite}
        />
      ),
    },
    {
      key: 3,
      label: "Cliente",
      permissionCode: "view_customers_tab",
      disabled: isDisable,
      children: (
        <ClientTab
          favorites={favorites}
          clientsComponents={clientComponents}
          components={components}
          setVisionFavorite={setVisionFavorite}
        />
      ),
    },
  ];

  const filteredComponents = allComponents.map((item) => {
    const permissionCodes = permissions?.data?.map((permission) => permission.code) || [];
    const disabled = !permissionCodes.includes(item.permissionCode);

    console.log('🔍 Cockpit: Filtro de abas:', {
      tab: item.label,
      permissionCode: item.permissionCode,
      disabled,
      availablePermissions: permissionCodes
    });

    return {
      ...item,
      disabled: disabled,
      children: !disabled && item.children,
    };
  });

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content
          style={{ padding: "2em", overflowX: "clip", overflowY: "auto" }}
        >
          <Card
            className="card-container"
            style={{
              width: "100%",
              borderRadius: "20px",
              backgroundColor: "rgba(0,0,0,0)",
            }}
          >
            <Tabs
              type="card"
              className="tabs"
              defaultActiveKey={1}
              items={filteredComponents}
            ></Tabs>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
