{"name": "dsm-front-end", "version": "0.5.8", "private": true, "dependencies": {"@amcharts/amcharts4": "^4.10.38", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@reduxjs/toolkit": "^2.0.1", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@tinymce/tinymce-react": "^4.3.2", "@types/moment": "^2.13.0", "alloyeditor": "^2.14.7", "amazon-cognito-identity-js": "*", "antd": "^5.12.8", "antd-mask-input": "^2.0.7", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "base32-decode": "^1.0.0", "base32-encode": "^2.0.0", "chart.js": "^4.4.1", "@craco/craco": "^6.4.3", "craco-antd": "^2.0.0", "date-fns": "^3.0.6", "dotenv": "^16.3.1", "draft-js": "^0.11.7", "draft-to-html": "0.0.1", "draftjs-to-html": "^0.9.1", "exceljs": "^4.4.0", "faker": "^5.5.3", "file-saver": "^2.0.5", "fs": "0.0.1-security", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "image-to-base64": "^2.2.0", "immer": "^10.0.3", "jodit-react": "^1.3.39", "jose": "^5.2.0", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "js-file-download": "^0.4.12", "jspdf": "^2.5.1", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "ms-teams-webhook": "^1.0.4", "nodemon": "^3.0.2", "pdfmake": "^0.2.8", "phosphor-react": "^1.4.1", "postcss": "^8.4.32", "qrcode": "^1.5.3", "rasterizehtml": "^1.3.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-collapsed": "^4.1.2", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-google-recaptcha": "^3.1.0", "react-icons": "^4.12.0", "react-json-view": "^1.21.3", "react-quill": "^2.0.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-toastify": "^9.1.3", "recharts": "^2.8.0", "redux": "^5.0.0", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "sass": "^1.69.5", "speakeasy": "^2.0.0", "swr": "^2.2.4", "tailwind": "^4.0.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "uuid": "^9.0.1", "uuidv4": "^6.2.13", "web-vitals": "^3.5.0", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.10.1", "webpackbar": "^6.0.1"}, "scripts": {"deploy-dev": "aws s3 sync ./build s3://hml.dsm.darede.com.br/ --acl public-read --profile hml-serverless-framework", "deploy-prod": "aws s3 sync ./build s3://dsm.darede.com.br/ --acl public-read --profile dosystems-serverless-framework", "start": "craco start", "start:watch": "nodemon -w craco.config.js -w ./antd.customize.less --exec \"npx craco start\"", "build": "npm run lint && craco --max_old_space_size=4096 build", "build:no-lint": "craco --max_old_space_size=4096 build", "build:analyze": "npm run build:no-lint && npx webpack-bundle-analyzer build/static/js/*.js", "test": "react-scripts test", "test-all": "react-scripts test --watchAll=false --coverage .", "test:ci": "react-scripts test --watchAll=false --coverage --testResultsProcessor=jest-sonar-reporter", "lint": "oxlint src/", "lint:fix": "oxlint src/ --fix", "type-check": "tsc --noEmit", "eject": "react-scripts eject"}, "eslintConfig": {"extends": []}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/js-cookie": "^3.0.6", "@types/react": "^18.2.45", "@types/react-color": "^3.0.9", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.2.1", "aws-sdk": "^2.1518.0", "axios-mock-adapter": "^1.22.0", "jsdom": "^23.0.1", "oxlint": "^0.9.9", "react-error-overlay": "^6.0.9", "text-encoding": "^0.7.0", "vite": "^5.0.8"}}