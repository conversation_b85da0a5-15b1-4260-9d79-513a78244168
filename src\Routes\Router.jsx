import { useEffect, useState, lazy } from "react";
import {
  <PERSON>rowser<PERSON><PERSON><PERSON>,
  Route,
  Routes,
  Navigate,
  useNavigate,
} from "react-router-dom";

// Core components (not lazy loaded for better UX)
import { Login } from "../pages/Login";
import MFA from "../pages/MFA";
import { Unauthorized } from "../pages/Unauthorized";
import { Logoff } from "../pages/Logoff";
import packageJson from "../../package.json";
import IdleTimer from "../service/logoutTimer";
import { verifyExpTime } from "../service/verifyExpTime";
import { authService } from "../services/authService";
// LazyRoute removido - usando imports diretos para compatibilidade React 18


// Lazy loaded pages for better performance
const Home = lazy(() => import("../pages/Home").then(module => ({ default: module.Home })));
const Billing = lazy(() => import("../pages/Billing").then(module => ({ default: module.Billing })));
const Clients = lazy(() => import("../pages/Clients").then(module => ({ default: module.Clients })));
const Audit = lazy(() => import("../pages/Audit").then(module => ({ default: module.Audit })));
const Invoices = lazy(() => import("../pages/Invoices").then(module => ({ default: module.Invoices })));
const Contracts = lazy(() => import("../pages/Contracts").then(module => ({ default: module.Contracts })));
const ManageUser = lazy(() => import("../pages/ManageUser").then(module => ({ default: module.ManageUser })));
const SecurityMFA = lazy(() => import("../pages/SecurityMFA").then(module => ({ default: module.SecurityMFA })));
const SwitchRoles = lazy(() => import("../pages/SwitchRoles").then(module => ({ default: module.SwitchRoles })));
const PermissionSets = lazy(() => import("../pages/PermissionSets").then(module => ({ default: module.PermissionSets })));
const ManagePermission = lazy(() => import("../pages/ManagePermission").then(module => ({ default: module.ManagePermission })));
const TicketsAnswer = lazy(() => import("../pages/TicketsAnswer").then(module => ({ default: module.TicketsAnswer })));
const ServicesCatalog = lazy(() => import("../pages/ServicesCatalog").then(module => ({ default: module.ServicesCatalog })));
const Service = lazy(() => import("../pages/Service").then(module => ({ default: module.Service })));
const ArticlesTickets = lazy(() => import("../pages/ArticlesTickets").then(module => ({ default: module.ArticlesTickets })));
const Tickets = lazy(() => import("../pages/Tickets").then(module => ({ default: module.Tickets })));
const TechnicalProposalList = lazy(() => import("../pages/TechnicalProposalList").then(module => ({ default: module.TechnicalProposalList })));
const TechnicalProposal = lazy(() => import("../pages/TechnicalProposal").then(module => ({ default: module.TechnicalProposal })));
const ManagmentServices = lazy(() => import("../pages/ManagementServices").then(module => ({ default: module.ManagmentServices })));
const ManageFunctions = lazy(() => import("../pages/ManageFunctions").then(module => ({ default: module.ManageFunctions })));
const CommercialProposal = lazy(() => import("../pages/CommercialProposal").then(module => ({ default: module.CommercialProposal })));
const CommercialProposalTemplate = lazy(() => import("../pages/CommercialProposalTemplate").then(module => ({ default: module.CommercialProposalTemplate })));
const CommercialProposalList = lazy(() => import("../pages/CommercialProposalList").then(module => ({ default: module.CommercialProposalList })));
const CostManagement = lazy(() => import("../pages/CostManagement").then(module => ({ default: module.CostManagement })));
const AddService = lazy(() => import("../pages/AddService").then(module => ({ default: module.AddService })));
const TechnicalProposalAdd = lazy(() => import("../pages/TechnicalProposalAdd").then(module => ({ default: module.TechnicalProposalAdd })));
const CommercialProposalRfs = lazy(() => import("../pages/CommercialProposalAdd").then(module => ({ default: module.CommercialProposalRfs })));
const TechnicalServiceEdit = lazy(() => import("../pages/TechnicalServiceEdit").then(module => ({ default: module.TechnicalServiceEdit })));
const EditManagmentServices = lazy(() => import("../pages/EditManagementServices").then(module => ({ default: module.EditManagmentServices })));
const Cockpit = lazy(() => import("../pages/Cockpit").then(module => ({ default: module.Cockpit })));
const CockpitPdms = lazy(() => import("../pages/CockpitPdms").then(module => ({ default: module.CockpitPdms })));
const CockpitAnalista = lazy(() => import("../pages/CockpitAnalista").then(module => ({ default: module.CockpitAnalista })));
const Activities = lazy(() => import("../pages/Activities").then(module => ({ default: module.Activities })));
const Files = lazy(() => import("../pages/Files").then(module => ({ default: module.Files })));
const ConsumptionHoursReport = lazy(() => import("../pages/Reports/ConsumptionHours").then(module => ({ default: module.ConsumptionHoursReport })));
const SwitchRolesAudits = lazy(() => import("../pages/SwitchRoleAudit").then(module => ({ default: module.SwitchRolesAudits })));
const TicketsReport = lazy(() => import("../pages/Reports/Tickets").then(module => ({ default: module.TicketsReport })));

function VerifyCache({ children }) {
  let version = localStorage.getItem("version");
  if (version !== packageJson.version) {
    if ("caches" in window) {
      caches.keys().then((names) => {
        names.forEach((name) => {
          caches.delete(name);
        });
      });

      window.location.reload();
    }

    localStorage.clear();
    localStorage.setItem("version", packageJson.version);
  }

  return children;
}

function RequireAuth({ children }) {
  const navigate = useNavigate();

  useEffect(() => {
    const verifySession = async () => {
      const data = await verifyExpTime(localStorage.getItem("jwt"));
      return data;
    };

    verifySession().then((res) => {
      if (res === true) {
        navigate("/logoff", { state: "logoff" });
      }
    });
  }, [navigate]);

  // Version check
  useEffect(() => {
    let version = localStorage.getItem("version");
    if (version !== packageJson.version) {
      if ("caches" in window) {
        caches.keys().then((names) => {
          names.forEach((name) => {
            caches.delete(name);
          });
        });
        window.location.reload();
      }
      localStorage.clear();
      localStorage.setItem("version", packageJson.version);
    }
  }, []);

  // LÓGICA SIMPLES DA VERSÃO ORIGINAL - APENAS VERIFICA @dsm/name
  if (["", null, undefined].includes(localStorage.getItem("@dsm/name")) === true) {
    console.log("🔄 Usuário não autenticado (@dsm/name ausente), redirecionando para login...");
    console.log("@dsm/name atual:", localStorage.getItem("@dsm/name"));
    return <Navigate to={"/login"} />;
  }

  console.log("✅ Usuário autenticado (@dsm/name presente), renderizando componente protegido");
  console.log("@dsm/name:", localStorage.getItem("@dsm/name"));
  console.log("🏠 Componente a ser renderizado:", children?.type?.name || "Componente desconhecido");
  return children;
}

function Router() {
  if (localStorage.getItem("@dsm/permission") === "none") {
    localStorage.clear();
    return <Navigate to={"/login"} />;
  }

  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <Routes>
        <Route
          path="/cockpit"
          element={
            <RequireAuth>
              <Cockpit />
            </RequireAuth>
          }
        />
        <Route
          path="/cockpit-pdms"
          element={
            <RequireAuth>
              <CockpitPdms />
            </RequireAuth>
          }
        />
        <Route
          path="/cockpit-analista"
          element={
            <RequireAuth>
              <CockpitAnalista />
            </RequireAuth>
          }
        />
        <Route
          path="/atividades"
          element={
            <RequireAuth>
              <Activities />
            </RequireAuth>
          }
        />
        <Route
          path="/login"
          element={
            <VerifyCache>
              <Login />
            </VerifyCache>
          }
        />
        <Route path="/mfa" element={<MFA />} />
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route
          path="/clients"
          element={
            <RequireAuth>
              <Clients />
            </RequireAuth>
          }
        />
        <Route
          path="/billing"
          element={
            <RequireAuth>
              <Billing />
            </RequireAuth>
          }
        />
        <Route
          path="/audit"
          element={
            <RequireAuth>
              <Audit />
            </RequireAuth>
          }
        />
        <Route
          path="/invoices"
          element={
            <RequireAuth>
              <Invoices />
            </RequireAuth>
          }
        />
        <Route
          path="/contracts"
          element={
            <RequireAuth>
              <Contracts />
            </RequireAuth>
          }
        />
        <Route
          path="/gerenciar/usuarios"
          element={
            <RequireAuth>
              <ManageUser />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/mfa"
          element={
            <RequireAuth>
              <SecurityMFA />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/permission-sets"
          element={
            <RequireAuth>
              <PermissionSets />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/acessos"
          element={
            <RequireAuth>
              <SwitchRoles />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/auditoria"
          element={
            <RequireAuth>
              <SwitchRolesAudits />
            </RequireAuth>
          }
        />
        <Route
          path="*"
          element={
            <RequireAuth>
              <Home />
            </RequireAuth>
          }
        />
        <Route path="/logoff" element={<Logoff />} />
        <Route
          path="/gerenciar/permissions"
          element={
            <RequireAuth>
              <ManagePermission />
            </RequireAuth>
          }
        />
        <Route
          path="/gerenciar/functions"
          element={
            <RequireAuth>
              <ManageFunctions />
            </RequireAuth>
          }
        />
        <Route
          path="/ticket"
          element={
            <RequireAuth>
              <TicketsAnswer />
            </RequireAuth>
          }
        />
        <Route
          path="/services"
          element={
            <RequireAuth>
              <ServicesCatalog />
            </RequireAuth>
          }
        />
        <Route
          path="/services/add"
          element={
            <RequireAuth>
              <AddService />
            </RequireAuth>
          }
        />
        <Route
          path="/services/edit"
          element={
            <RequireAuth>
              <Service />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals-services/edit"
          element={
            <RequireAuth>
              <TechnicalServiceEdit />
            </RequireAuth>
          }
        />
        <Route
          path="/service-management"
          element={
            <RequireAuth>
              <ManagmentServices />
            </RequireAuth>
          }
        />
        <Route
          path="/service-management/edit"
          element={
            <RequireAuth>
              <EditManagmentServices />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals"
          element={
            <RequireAuth>
              <TechnicalProposalList />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals/add"
          element={
            <RequireAuth>
              <TechnicalProposalAdd />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals/template/:templateID"
          element={
            <RequireAuth>
              <TechnicalProposalAdd />
            </RequireAuth>
          }
        />

        <Route
          path="/technical-proposals/edit/:proposalID"
          element={
            <RequireAuth>
              <TechnicalProposalAdd />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposal"
          element={
            <RequireAuth>
              <CommercialProposalList />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposals/add"
          element={
            <RequireAuth>
              <CommercialProposalRfs />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposals/edit"
          element={
            <RequireAuth>
              <CommercialProposal />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposals/template"
          element={
            <RequireAuth>
              <CommercialProposalTemplate />
            </RequireAuth>
          }
        />
        <Route
          path="/cost-management"
          element={
            <RequireAuth>
              <CostManagement />
            </RequireAuth>
          }
        />
        <Route
          path="/tickets"
          element={
            <RequireAuth>
              <Tickets />
            </RequireAuth>
          }
        />
        <Route
          path="/articles"
          element={
            <RequireAuth>
              <ArticlesTickets />
            </RequireAuth>
          }
        />
        <Route
          path="/ticket-answer"
          element={
            <RequireAuth>
              <TicketsAnswer />
            </RequireAuth>
          }
        />
        <Route
          path="/reports/consumption-hours"
          element={
            <RequireAuth>
              <ConsumptionHoursReport />
            </RequireAuth>
          }
        />
        <Route
          path="/files"
          element={
            <RequireAuth>
              <Files />
            </RequireAuth>
          }
        />
        <Route
          path="/reports/tickets"
          element={
            <RequireAuth>
              <TicketsReport />
            </RequireAuth>
          }
        />
      </Routes>
    </BrowserRouter>
  );
}

export default Router;
