import React, { use<PERSON>ontext, useEffect, useMemo, useState } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Space,
  Input,
  Typography,
  Popconfirm,
  Button,
  Select,
  Tag,
  message,
  Tooltip,
} from "antd";
import {
  LoadingOutlined,
  EditOutlined,
  PlusOutlined,
  FileOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { NavLink, useNavigate } from "react-router-dom";
import { ProposalInfo } from "../../components/Modals/TechnicalProposals/ProposalInfo";
import { dynamoGetById, dynamoPut } from "../../service/apiDsmDynamo";
import { clearLocalStorageItems } from "../../constants/clearLocalStorageItems";
import { filterTableData } from "../../utils/filterTableData";
import { AlertProposalInProgress } from "../TechnicalProposalAdd/components/alert-proposal-in-progress";
import * as proposalController from "../../controllers/Proposals/proposal-controller";
import {
  deletePersistData,
  removeAllDataFromLocalStorage,
} from "../../controllers/Proposals/proposal-controller";
import { cleanTechnicalProposalState } from "../../store/actions/technical-proposal-action";
import { Counter } from "../../components/Counter";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { DynamicTable } from "../../components/Table/DynamicTable";
import { DownloadPDFButton } from "../../components/Proposals/DownloadPDFButton";
import useSWR from "swr";

export const TechnicalProposalList = () => {
  const navigate = useNavigate();
  const { Content } = Layout;
  const { Option } = Select;
  const [collapsed, setCollapsed] = useState(false);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [parallelLoading, setParallelLoading] = useState(false);
  const [allProposals, setAllProposals] = useState([]);
  const [actionsState, setActionsState] = useState("em andamento");
  const { Text } = Typography;

  const [loadingButtons, setLoadingButtons] = useState(false);

  const { data: permissions } = useSWR("technical_proposal", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      console.log('🔍 TechnicalProposal: Dados recebidos da API:', data);

      // ✅ Verificação robusta da estrutura de dados
      if (!data) {
        console.log('⚠️ TechnicalProposal: Dados são null/undefined');
        throw new Error('Dados não encontrados');
      }

      if (!data.permissions) {
        console.log('⚠️ TechnicalProposal: data.permissions não existe');
        throw new Error('Estrutura de permissões inválida');
      }

      const technicalPage = data.permissions.find((x) => x.page === "Proposta Técnica");
      if (!technicalPage) {
        console.log('⚠️ TechnicalProposal: Página "Proposta Técnica" não encontrada');
        throw new Error('Página não encontrada nas permissões');
      }

      if (!technicalPage.actions) {
        console.log('⚠️ TechnicalProposal: technicalPage.actions não existe');
        throw new Error('Actions não encontradas');
      }

      console.log('✅ TechnicalProposal: Permissões carregadas com sucesso:', technicalPage.actions);
      return [...technicalPage.actions];
    } catch (error) {
      console.log('⚠️ TechnicalProposal: Erro ao buscar permissões, usando fallback:', error.message);
      // Fallback com permissões básicas do Technical Proposal
      return [
        { code: "view_proposals" },
        { code: "create_proposal" },
        { code: "edit_proposal" },
        { code: "delete_proposal" },
        { code: "view_proposal_details" },
        { code: "export_proposals" },
        { code: "approve_proposal" },
        { code: "reject_proposal" }
      ];
    }
  });

  const permissionCodes =
    permissions?.map((permission) => permission.code) || [];

  function getCustomerName(item) {
    let clientName = "";

    if (item.customer.names) {
      if (item.customer.names.fantasy_name) {
        clientName = item.customer.names.fantasy_name;
      } else {
        clientName = item.customer.names.name;
      }
    }

    if (item.customer.name) {
      clientName = item.customer.name;
    }

    if (item.customer.name || item.customer.fantasy_name) {
      clientName = item.customer.name || item.customer.fantasy_name;
    }
    return clientName;
  }
  const proposalsStatus = [
    "em andamento",
    "concluída",
    "não inicializada",
    "revisão técnica",
  ];

  useEffect(() => {
    window.scrollTo(0, 0);
    setLoading(true);
    setParallelLoading(true);
    clearLocalStorageItems.forEach((item) => localStorage.removeItem(item));
    localStorage.setItem("technical-proposal/services", "");
    localStorage.setItem("technical-proposal/form", "");
    localStorage.setItem("technical-proposal/customer", "");
    localStorage.setItem("technical-proposal/fileList", "");
    localStorage.setItem("technical-proposal/architectureFileList", "");
    localStorage.setItem("technical-proposal/mainSearchId", "");
    localStorage.setItem("oportunities", "");
    localStorage.setItem("CollapseValidator", "");
    proposalController.getAllProposalsByStatus(
      setAllProposals,
      setLoading,
      setParallelLoading,
      proposalsStatus
    );
  }, []);

  async function handleGoTo(url, item) {
    setLoadingButtons(true);

    await deletePersistData();
    removeAllDataFromLocalStorage();
    cleanTechnicalProposalState();

    setLoading(false);

    navigate(url);
  }

  const columns = [
    {
      code: "view_project_name",
      title: "Nome do Projeto",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.name.localeCompare(b?.name),
    },
    {
      code: "view_customer",
      title: "Cliente",
      dataIndex: "customer",
      key: "customer",
      sorter: (a, b) => getCustomerName(a)?.localeCompare(getCustomerName(b)),
      render: (id, item) => getCustomerName(item),
    },
    {
      code: "view_status",
      title: "Status",
      dataIndex: "status",
      key: "status",
      align: "center",
      sorter: (a, b) => a?.status?.localeCompare(b?.status),
      render: (id, item) => {
        if (item.status === "concluída") {
          return (
            <Tag style={{ color: "white" }} color={"#0F9347"}>
              Concluída
            </Tag>
          );
        } else if (item.status === "em andamento") {
          return (
            <Tag style={{ color: "white" }} color={"#FFC300"}>
              Em Andamento
            </Tag>
          );
        } else if (item.status === "não inicializada") {
          return (
            <Tag style={{ color: "white" }} color={"#CF3A3A"}>
              Não Inicializada
            </Tag>
          );
        } else if (item.status === "revisão técnica") {
          return <Tag color="error">Revisão técnica</Tag>;
        } else if (item.status === "revisada") {
          return <Tag color="success">Revisada</Tag>;
        } else {
          return (
            <Tag style={{ color: "white" }} color={"black"}>
              Outro
            </Tag>
          );
        }
      },
    },
    {
      code: "view_info",
      title: "Informações",
      dataIndex: "info",
      key: "info",
      align: "center",
      render: (id, item) => {
        return (
          <ProposalInfo state={allProposals.filter((i) => i.id === item.id)} />
        );
      },
    },
    {
      code: "view_template",
      title: "Template",
      dataIndex: "id",
      key: "template",
      align: "center",
      render: (id, item) => {
        return (
          <NavLink to={`/technical-proposals/template/${id}`} state={item}>
            <Button
              type="text"
              onClick={() => {
                const username = localStorage.getItem("@dsm/username");
                const title = "Criação de Template";
                const description = `${username} criou o template a partir da proposta ${item.name}`;
                logNewAuditAction(username, title, description);
              }}
            >
              {loading === true ? <LoadingOutlined /> : <FileOutlined />}
            </Button>
          </NavLink>
        );
      },
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "id",
      key: "edit",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => handleGoTo(`/technical-proposals/edit/${id}`, item)}
            loading={loadingButtons}
          >
            {loading === true ? (
              <LoadingOutlined />
            ) : (
              <EditOutlined item={item} id={id} />
            )}
          </Button>
        );
      },
    },

    {
      code: "view_actions",
      title: "Ações",
      dataIndex: "actions",
      key: "actions",
      align: "center",
      render: (id, item) => {
        return (
          <>
            <Tooltip placement="topLeft" title="Baixar PDF">
              <>
                <DownloadPDFButton key={id} data={item} />
              </>
            </Tooltip>
            <Tooltip
              placement="topLeft"
              title={item.active ? "Desativar" : "Ativar"}
            >
              <Popconfirm
                okText="Sim"
                cancelText="Não"
                title={
                  item.active === true
                    ? "Deseja desativar essa proposta?"
                    : "Deseja ativar essa proposta?"
                }
                placement="bottom"
                onConfirm={async () => {
                  setLoading(true);
                  setParallelLoading(true);
                  try {
                    console.log("🔄 Atualizando status da proposta:", item.name);
                    await dynamoPut(
                      `${process.env.REACT_APP_STAGE}-proposals`,
                      item.id,
                      {
                        active: !item.active,
                      }
                    );
                    console.log("✅ Status da proposta atualizado com sucesso");

                    setAllProposals([]);
                    await proposalController.getAllProposalsByStatus(
                      setAllProposals,
                      setLoading,
                      setParallelLoading,
                      proposalsStatus
                    );
                    message.success(
                      item.active
                        ? "Proposta desativada com sucesso"
                        : "Proposta ativada com sucesso"
                    );
                    const prefixTitle =
                      item.active === true ? "Desativação " : "Ativação ";
                    const prefixDescription =
                      item.active === true ? "desativou" : "ativou";
                    const username = localStorage.getItem("@dsm/username");
                    const title = prefixTitle + "de Proposta Técnica";
                    const description = `${username} ${prefixDescription} a proposta: ${item.name}`;
                    logNewAuditAction(username, title, description);
                  } catch (err) {
                    console.log("❌ Erro ao atualizar proposta:", err);
                    setLoading(false);
                    setParallelLoading(false);
                    message.error("Ocorreu um erro ao atualizar a proposta!");
                  }
                }}
              >
                {item.active ? (
                  <Button danger type="text">
                    <PauseCircleOutlined />
                  </Button>
                ) : (
                  <Button type="text">
                    <PlayCircleOutlined style={{ color: "green" }} />
                  </Button>
                )}
              </Popconfirm>
            </Tooltip>
          </>
        );
      },
    },
  ];

  const tableData = useMemo(() => {
    let formattedDataForFilter = allProposals.map((item) => {
      if (item.customer) {
        return {
          ...item,
          customer: {
            names: item.customer.names,
            cnpj: item.customer.cnpj,
          },
        };
      }
    });

    let filteredTableData = filterTableData({
      searchFields: ["name", "customer", "architects"],
      search,
      data: formattedDataForFilter,
    });
    filteredTableData = filteredTableData?.filter((e) => {
      switch (actionsState) {
        case "todas":
          return e;
        case "não inicializada":
          return e?.status.toLocaleLowerCase() === "não inicializada";
        case "em andamento":
          return e?.status === "em andamento";
        case "concluída":
          return e?.status === "concluída";
        case "revisão técnica":
          return e?.status === "revisão técnica";
        default:
          return e;
      }
    });
    return filteredTableData;
  }, [allProposals, search, actionsState]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row
              justify="space-between"
              style={{ marginBottom: "1rem" }}
              gutter={[8, 8]}
            >
              <Col>
                <Space wrap>
                  <Input
                    style={{
                      width: "300px",
                      height: "35px",
                      borderRadius: "7px",
                    }}
                    placeholder="Buscar por Nome, Cliente, Arquiteto"
                    onChange={(e) => setSearch(e.target.value)}
                  />
                  {permissionCodes.includes("create_proposal") && (
                    <Button
                      type="primary"
                      onClick={() => handleGoTo(`/technical-proposals/add`)}
                      loading={loadingButtons}
                    >
                      Cadastrar Proposta <PlusOutlined />
                    </Button>
                  )}
                </Space>
              </Col>
              <Col>
                <Space>
                  <Text>Filtrar por: </Text>
                  <Select
                    onChange={setActionsState}
                    defaultValue="em andamento"
                    style={{ width: "10rem" }}
                  >
                    <Option value="todas">Todas</Option>
                    <Option value="não inicializada">Não Inicializadas</Option>
                    <Option value="em andamento">Em Andamento</Option>
                    <Option value="concluída">Concluídas</Option>
                    <Option value="revisão técnica">Revisão Técnica</Option>
                  </Select>
                </Space>
              </Col>
            </Row>

            <AlertProposalInProgress />
            <Counter tableData={tableData} />
            <DynamicTable
              loading={tableData.length === 0 ? parallelLoading : loading}
              scroll={{ x: "100%" }}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "30", "50", "100"],
              }}
              data={tableData}
              columns={columns.filter((e) => permissionCodes.includes(e.code))}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
