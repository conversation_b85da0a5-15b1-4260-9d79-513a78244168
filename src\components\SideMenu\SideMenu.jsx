import React from "react";
import {
  HomeOutlined,
  IdcardOutlined,
  UserAddOutlined,
  CalendarOutlined,
  UserSwitchOutlined,
  UsergroupDeleteOutlined,
  AppstoreAddOutlined,
  BookOutlined,
  UnorderedListOutlined,
  DollarCircleOutlined,
  FileTextOutlined,
  MailOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  FundOutlined,
  Slide<PERSON>Outlined,
  <PERSON><PERSON>hartOutlined,
  FolderOpenOutlined,
} from "@ant-design/icons";
import { FaRegUserCircle, FaRegSave } from "react-icons/fa";
import { Menu, Layout, Drawer } from "antd";
import { GoGraph } from "react-icons/go";
import { Link } from "react-router-dom";
import { HiCash } from "react-icons/hi";
import useSWR from "swr";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import packageJson from "../../../package.json";
import "./SideMenu.css";

export const SideMenu = (props) => {
  const { collapsed, setCollapsed } = props;
  const { Sider } = Layout;

  const permissions = useSWR("menu", async () => {
    const userPermission = localStorage.getItem("@dsm/permission");

    if (!userPermission || userPermission === 'null' || userPermission === 'undefined') {
      console.warn('⚠️ SideMenu: Permissão do usuário não encontrada no localStorage');
      return []; 
    }

    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        userPermission
      );

      // FALLBACK PARA DESENVOLVIMENTO LOCAL (igual ao Home)
      if (!data?.permissions) {
        console.log('🔧 SideMenu: Usando permissões padrão (fallback)');
        data = {
          permissions: [
            { page: "Home" },
            { page: "Cockpit SDM" },
            { page: "Cockpit PDM" },
            { page: "Cockpit Analista" },
            { page: "Cockpit Atividades" },
            { page: "Tickets" },
            { page: "Clientes" },
            { page: "Propostas" },
            { page: "Relatórios" },
            { page: "Configurações" }
          ]
        };
      }

      return [...data.permissions];
    } catch (error) {
      console.log('⚠️ SideMenu: Erro ao buscar permissões, usando fallback:', error.message);
      // Fallback em caso de erro
      return [
        { page: "Home" },
        { page: "Cockpit SDM" },
        { page: "Cockpit PDM" },
        { page: "Cockpit Analista" },
        { page: "Cockpit Atividades" },
        { page: "Tickets" },
        { page: "Clientes" },
        { page: "Propostas" },
        { page: "Relatórios" },
        { page: "Configurações" }
      ];
    }
  });

  function verify(permission) {
    return permissions?.data
      ?.map(({ page }) => {
        return page;
      })
      .includes(permission);
  }

  const MenuOptions = () => {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        <Menu
          defaultSelectedKeys={[window.location.pathname]}
          theme="dark"
          style={{ backgroundColor: "rgb(53, 53, 53)" }}
        >
          {verify("Home") ||
          verify("Cockpit SDM") ||
          verify("Cockpit PDM") ||
          verify("Cockpit Analista") ||
          verify("Cockpit Atividades") ? (
            <Menu.ItemGroup style={{ marginTop: 20 }} title="Dashboard">
              {verify("Home") ? (
                <Menu.Item key="/" icon={<HomeOutlined />}>
                  <Link to="/">Home</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Cockpit SDM") && (
                <Menu.Item key="/cockpit" icon={<AreaChartOutlined />}>
                  <Link to="/cockpit">Cockpit - SDM</Link>
                </Menu.Item>
              )}
              {verify("Cockpit PDM") && (
                <Menu.Item key="/cockpit-pdms" icon={<FundOutlined />}>
                  <Link to="/cockpit-pdms">Cockpit - PDM</Link>
                </Menu.Item>
              )}
              {verify("Cockpit Analista") && (
                <Menu.Item key="/cockpit-analista" icon={<SlidersOutlined />}>
                  <Link to="/cockpit-analista">Cockpit - Analista</Link>
                </Menu.Item>
              )}
              {verify("Cockpit Atividades") && (
                <Menu.Item key="/atividades" icon={<RadarChartOutlined />}>
                  <Link to="/atividades">Atividades</Link>
                </Menu.Item>
              )}
            </Menu.ItemGroup>
          ) : (
            ""
          )}
          {verify("Clientes") ||
          verify("Contratos") ||
          verify("Tickets") ||
          verify("Gerenciamento de Custos") ? (
            <Menu.ItemGroup title="Administração">
              {verify("Clientes") ? (
                <Menu.Item key="/clients" icon={<FaRegUserCircle />}>
                  <Link to="/clients">Clientes</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Contratos") ? (
                <Menu.Item key="/contracts" icon={<CalendarOutlined />}>
                  <Link to="/contracts">Contratos</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Gerenciamento de Custos") && (
                <Menu.Item
                  key="/cost-management"
                  icon={<DollarCircleOutlined />}
                >
                  <Link to="/cost-management">Gerenciamento de Custos</Link>
                </Menu.Item>
              )}
            </Menu.ItemGroup>
          ) : (
            ""
          )}

          {(verify("Catálogo de Serviços") || verify("Proposta Técnica")) && (
            <Menu.ItemGroup title="Pré-vendas">
              {verify("Catálogo de Serviços") && (
                <Menu.Item key="/services" icon={<BookOutlined />}>
                  <Link to="/services">Catálogos de Serviços</Link>
                </Menu.Item>
              )}

              {verify("Proposta Técnica") && (
                <Menu.Item
                  key="/technical-proposals"
                  icon={<FileTextOutlined />}
                >
                  <Link to="/technical-proposals">Propostas Técnicas</Link>
                </Menu.Item>
              )}
            </Menu.ItemGroup>
          )}

          {verify("Proposta Comercial") && (
            <Menu.ItemGroup title="Comercial">
              {verify("Proposta Comercial") && (
                <Menu.Item
                  key="/commercial-proposal"
                  icon={<UnorderedListOutlined />}
                >
                  <Link to="/commercial-proposal">Propostas Comerciais</Link>
                </Menu.Item>
              )}
            </Menu.ItemGroup>
          )}

          {verify("Billing") ||
          verify("Invoices") ||
          verify("Auditoria") ||
          verify("Reports") ||
          verify("Arquivo") ? (
            <Menu.ItemGroup title="Gerencial">
              {verify("Billing") ? (
                <Menu.Item key="/billing" icon={<FaRegSave />}>
                  <Link to="/billing">Billing</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Invoices") ? (
                <Menu.Item key="/invoices" icon={<HiCash />}>
                  <Link to="/invoices">Invoices</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Auditoria") ? (
                <Menu.Item key="/audit" icon={<GoGraph />}>
                  <Link to="/audit">Auditoria</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Arquivo") ? (
                <Menu.Item key="/files" icon={<FolderOpenOutlined />}>
                  <Link to="/files">Arquivos</Link>
                </Menu.Item>
              ) : (
                ""
              )}
            </Menu.ItemGroup>
          ) : (
            ""
          )}

          {verify("Consumo de Horas") ? (
            <Menu.ItemGroup title="Relatórios">
              {verify("Consumo de Horas") ? (
                <Menu.Item key="/reports/consumption-hours" icon={<GoGraph />}>
                  <Link to="/reports/consumption-hours">Consumo de Horas</Link>
                </Menu.Item>
              ) : (
                ""
              )}

              {verify("Relatório de Tickets") ? (
                <Menu.Item key="/reports/tickets" icon={<GoGraph />}>
                  <Link to="/reports/tickets">Tickets</Link>
                </Menu.Item>
              ) : (
                ""
              )}
            </Menu.ItemGroup>
          ) : (
            ""
          )}

          {verify("Switch Roles") || verify("Permission Sets") ? (
            <Menu.ItemGroup title="Segurança">
              {verify("Permission Sets") ? (
                <Menu.Item
                  key="/seguranca/permission-sets"
                  icon={<IdcardOutlined />}
                >
                  <Link to="/seguranca/permission-sets">Permission Sets</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Switch Roles") ? (
                <Menu.Item
                  key="/seguranca/acessos"
                  icon={<UserSwitchOutlined />}
                >
                  <Link to="/seguranca/acessos">Switch Roles</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Histórico de Ações") ? (
                <Menu.Item key="/seguranca/auditoria" icon={<GoGraph />}>
                  <Link to="/seguranca/auditoria">Histórico de Ações</Link>
                </Menu.Item>
              ) : (
                ""
              )}
            </Menu.ItemGroup>
          ) : (
            ""
          )}
          {verify("Gerenciar Usuários") ||
          verify("Gerenciar Permissoes") ||
          verify("Gerenciar Funcionalidades") ? (
            <Menu.ItemGroup title="Acessos">
              {verify("Gerenciar Usuários") ? (
                <Menu.Item
                  key="/gerenciar/usuarios"
                  icon={<UsergroupDeleteOutlined />}
                >
                  <Link to="/gerenciar/usuarios">Gerenciar Usuários</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              {verify("Gerenciar Permissoes") ? (
                <Menu.Item
                  key="/gerenciar/permissions"
                  icon={<UserAddOutlined />}
                >
                  <Link to="/gerenciar/permissions">Gerenciar Permissões</Link>
                </Menu.Item>
              ) : (
                ""
              )}
              <Menu.Item
                key="/gerenciar/functions"
                icon={<AppstoreAddOutlined />}
              >
                <Link to="/gerenciar/functions">Gerenciar Funcionalidades</Link>
              </Menu.Item>
            </Menu.ItemGroup>
          ) : (
            ""
          )}

          {verify("Gerenciar Usuários") ||
          verify("Gerenciar Permissoes") ||
          verify("Gerenciar Funcionalidades") ? (
            <Menu.ItemGroup title="Automação">
              {verify("Gerenciar Usuários") ? (
                <Menu.Item key="/automation/email" icon={<MailOutlined />}>
                  <Link to="/automation/email">Email</Link>
                </Menu.Item>
              ) : (
                ""
              )}
            </Menu.ItemGroup>
          ) : (
            ""
          )}
        </Menu>

        <div style={{ width: "100%", textAlign: "center", color: "#ccc" }}>
          <p>
            {process.env.REACT_APP_STAGE !== "prod" ? (
              <>
                v{packageJson.version} em {process.env.REACT_APP_STAGE}
              </>
            ) : (
              ""
            )}
          </p>
        </div>
      </div>
    );
  };

  return window.innerWidth <= 500 ? (
    <Drawer
      bodyStyle={{ backgroundColor: "rgb(53, 53, 53)" }}
      closable={false}
      placement="left"
      key="left"
      onClose={() => setCollapsed(false)}
      open={collapsed}
      width="300px"
    >
      <MenuOptions />
    </Drawer>
  ) : (
    <Sider
      style={{ backgroundColor: "rgb(53, 53, 53)" }}
      collapsible
      trigger={null}
      collapsed={collapsed}
      width="245px"
    >
      <MenuOptions />
    </Sider>
  );
};
