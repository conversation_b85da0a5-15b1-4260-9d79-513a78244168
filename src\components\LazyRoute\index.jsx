import React, { Suspense } from 'react';
import { Spin } from 'antd';

/**
 * LazyRoute Component
 * Provides a loading fallback for lazy-loaded components
 */
const LazyRoute = ({ children, fallback = null }) => {
  const defaultFallback = (
    <div 
      style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}
    >
      <Spin size="large" />
      <div>Carregando...</div>
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

export default LazyRoute;
