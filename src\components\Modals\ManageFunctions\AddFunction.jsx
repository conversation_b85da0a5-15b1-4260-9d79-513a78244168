import { DeleteOutlined, PlusOutlined, SwapOutlined } from "@ant-design/icons";
import { Button, Modal, Row, Col, Input, Form, message, Select } from "antd";
import { dynamoPut } from "../../../service/apiDsmDynamo";
import { useState } from "react";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const AddFunction = ({ permission, mutate }) => {
  const { Option } = Select;
  const [form] = Form.useForm();
  const [newPage, setNewPage] = useState(false);
  const [showModal, setShowModal] = useState();
  const [loading, setLoading] = useState(false);
  const [currentFunction, setCurrentFunction] = useState({
    id: "",
    page: "",
    permissions: [{ actions: "", code: "" }],
  });

  const handleSubmit = async (data) => {
    try {
      setLoading(true);

      console.log('🔍 AddFunction: Dados recebidos:', {
        data,
        permission: permission?.id,
        permissionAll: permission?.all?.length || 0
      });

      // Validar se permission e permission.id existem
      if (!permission || !permission.id) {
        console.error('⚠️ AddFunction: Permission ou permission.id não existe:', permission);
        message.error("Erro: Dados de permissão inválidos. Recarregue a página.");
        setLoading(false);
        return;
      }

      const page_names = permission.all?.map(({ page }) => page) || [];

      if (data.permissions === undefined || data.permissions.length === 0) {
        setLoading(false);
        return message.error("Adicione pelo menos uma função");
      } else if (page_names.includes(data.page) && newPage === true) {
        setLoading(false);
        return message.warning(
          "Esta página já existe no sistema, selecione outra opção."
        );
      } else if (newPage) {
        permission.all.push(data);

        console.log('🔍 AddFunction: Salvando nova página:', {
          table: `${process.env.REACT_APP_STAGE}-page-actions`,
          id: permission.id,
          dataLength: permission.all?.length || 0
        });

        await dynamoPut(
          `${process.env.REACT_APP_STAGE}-page-actions`,
          permission.id,
          permission
        );

      mutate({ all: [...permission.all] }, false);

      const username = localStorage.getItem("@dsm/username");
      const title = "Adição de Funcionalidade";
      const description = `${username} adicionou funcionalidades para a página de ${data.page}`;
      logNewAuditAction(username, title, description);

      message.success("A página e suas funções foram criadas com sucesso!");

      setShowModal(false);
      form.resetFields();
      setNewPage(false);
    } else {
      const existingPage = permission.all.find((p) => p.page === data.page);
      if (existingPage) {
        existingPage.permissions.push(...data.permissions);
      } else {
        console.error('⚠️ AddFunction: Página não encontrada:', data.page);
        message.error("Erro: Página não encontrada.");
        setLoading(false);
        return;
      }

      console.log('🔍 AddFunction: Adicionando permissões à página existente:', {
        page: data.page,
        newPermissions: data.permissions?.length || 0
      });

      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-page-actions`,
        permission.id,
        permission
      );

      const username = localStorage.getItem("@dsm/username");
      const title = "Adição de Funcionalidade";
      const description = `${username} adicionou funcionalidades para a página de ${data.page}`;
      logNewAuditAction(username, title, description);

      mutate(
        {
          all: permission.all.map((p) => {
            if (p.page === data.page) {
              return {
                ...p,
                permissions: [...p.permissions],
              };
            }

            return p;
          }),
        },
        false
      );

      message.success("Funções da página atualizadas com sucesso!");

      setShowModal(false);
      form.resetFields();
      setNewPage(false);
    }

    setLoading(false);
    } catch (error) {
      console.error('⚠️ AddFunction: Erro ao salvar função:', error);
      message.error(`Erro ao salvar função: ${error.message}`);
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        icon={<PlusOutlined />}
        type="primary"
        onClick={() => {
          setShowModal(true);
        }}
      >
        Adicionar Funcionalidade
      </Button>
      <Modal
        title={
          <Row justify="space-between" align="middle">
            <Col>Adicionar funções</Col>
            <Col>
              <Button
                type="primary"
                icon={<SwapOutlined />}
                onClick={() => setNewPage(!newPage)}
              >
                {newPage === false
                  ? "Nova página e funções"
                  : "Página existente"}
              </Button>
            </Col>
          </Row>
        }
        open={showModal}
        closable={false}
        onOk={() => form.submit()}
        confirmLoading={loading}
        okText="Salvar"
        cancelText="Cancelar"
        onCancel={() => {
          setShowModal(false);
          form.resetFields();
        }}
      >
        <Form
          form={form}
          name="form"
          layout="vertical"
          requiredMark={false}
          onFinish={handleSubmit}
        >
          {newPage === false && permission?.all !== undefined && (
            <Row>
              <Col span={24}>
                <Form.Item
                  name="page"
                  label="Selecione uma página já existente"
                >
                  <Select placeholder="Selecione uma página">
                    {permission?.all?.map((item) => {
                      return (
                        <>
                          <Option value={item.page}>{item.page}</Option>
                        </>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          {newPage === true && (
            <Row justify="center">
              <Col span={24}>
                <Form.Item
                  label="Insira o nome de uma nova página"
                  name="page"
                  rules={[
                    {
                      required: true,
                      message: "Por favor, insira o nome da página",
                    },
                  ]}
                >
                  <Input
                    placeholder="ex.: Clientes"
                    required
                    onChange={(e) => {
                      setCurrentFunction({
                        ...currentFunction,
                        page: e.target.value,
                      });
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Form.List name="permissions">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row gutter={[8, 8]} key={key}>
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, "action"]}
                        rules={[
                          {
                            required: true,
                            message: "Faltando a ação",
                          },
                        ]}
                      >
                        <Input placeholder="ex.: Visualizar CRM" />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, "code"]}
                        rules={[
                          {
                            required: true,
                            message: "Faltando o código",
                          },
                        ]}
                      >
                        <Input placeholder="ex.: view_crm" />
                      </Form.Item>
                    </Col>
                    <Col align="center" span={2}>
                      <DeleteOutlined
                        style={{ paddingTop: 8, color: "red" }}
                        onClick={() => remove(name)}
                      />
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    Adicionar função
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </>
  );
};
