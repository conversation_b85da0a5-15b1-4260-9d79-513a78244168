import { useEffect, useMemo, useState } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Space,
  Typography,
  Select,
  Tag,
  Popconfirm,
  message,
  Button,
} from "antd";
import { ClientUsersModal } from "../../components/Modals/Clients/ClientUsersModal";
import { ClientContractsModal } from "../../components/Modals/Clients/ClientContractsModal";
import { ClientAccountsModal } from "../../components/Modals/Clients/ClientAccountsModal";
import { ClientEditModal } from "../../components/Modals/Clients/ClientEdit";
import { customerSelectOptions } from "./constants/selectOptions";
import { ClientCreateModal } from "../../components/Modals/Clients/ClientCreate";
import { VisualizeInfo } from "../../components/Modals/Clients/VisualizeInfo";
import {
  dynamoGetById,
  dynamoPut,
  dynamoSwitchRoleAccessesPerUser,
  useDynamoGet,
} from "../../service/apiDsmDynamo";
import { otrsPut } from "../../service/apiOtrs";
import useSWR from "swr";
import { format, isValid } from "date-fns";
import { Counter } from "../../components/Counter";
import { shallowEqual, useSelector } from "react-redux";
import {
  setActiveCustomersWithActiveContractState,
  setActiveCustomersWithInactiveContractState,
  setAllCustomersState,
  setInactivesCustomersState,
  setProspectsState,
  setCustomersState,
  setSearchState,
} from "../../store/actions/customers-action";

import * as contractsController from "../../controllers/contracts/contract-controller";
import {
  filterBySearch,
  getCustomerByParameter,
} from "../../controllers/clients/clientsController";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { getCustomerName } from "../../utils/getCustomerName";
import { DynamicTable } from "../../components/Table/DynamicTable";
import { DateFilters } from "./components/ClientDateFilter";
import { ClientExportReportModal } from "../../components/Modals/Clients/ClientExportReportModal";
import moment from "moment";

const { Content } = Layout;
const { Text } = Typography;
const { Option } = Select;

export const Clients = () => {
  const activeCustomersWithActiveContract = useSelector(
    (state) => state.customers.activeCustomersWithActiveContract,
    shallowEqual
  );
  const activeCustomersWithInactiveContract = useSelector(
    (state) => state.customers.activeCustomersWithInactiveContract,
    shallowEqual
  );
  const inactiveCustomers = useSelector(
    (state) => state.customers.inactiveCustomers,
    shallowEqual
  );
  const prospects = useSelector(
    (state) => state.customers.prospects,
    shallowEqual
  );
  const allCustomers = useSelector(
    (state) => state.customers.allCustomers,
    shallowEqual
  );
  const search = useSelector((state) => state.customers.search, shallowEqual);
  const state = useSelector((state) => state.customers.state, shallowEqual);
  const dtEnd = useSelector((state) => state.customers.dtEnd, shallowEqual);
  const dtStart = useSelector((state) => state.customers.dtStart, shallowEqual);

  const permissions = useSWR("clients", async () => {
    let data = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-permissions`,
      localStorage.getItem("@dsm/permission")
    );
    let permissions = [
      ...data.permissions.find((x) => x.page === "Clientes").actions,
    ];

    return permissions;
  });
  const [userAccesses, setUserAccesses] = useState([]);

  const contractsPermissions = useSWR("allPermissions", async () => {
    let data = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-permissions`,
      localStorage.getItem("@dsm/permission")
    );

    return [...data.permissions.find((x) => x.page === "Contratos").actions];
  });

  const executives = useDynamoGet(`${process.env.REACT_APP_STAGE}-executives`);
  const tables = useDynamoGet(`${process.env.REACT_APP_STAGE}-wallets`);
  const permissionSets = useDynamoGet(`sso-accounts`);
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState();

  const handleStateChange = (value) => {
    setCustomersState(value);
  };

  const getActiveCustomersWithActiveContract = async () => {
    const route = "customers/read/hasActiveContracts";
    const customers = await getCustomerByParameter(route, { status: 1 });
    setActiveCustomersWithActiveContractState(customers);
  };

  const getActiveCustomersWithInactiveContract = async () => {
    const route = "customers/read/hasActiveContracts";
    const customers = await getCustomerByParameter(route, { status: 0 });
    setActiveCustomersWithInactiveContractState(customers);
  };

  const getInactiveCustomers = async () => {
    const route = "customers/read/status";
    let customers = await getCustomerByParameter(route, {
      status: 0,
    });
    customers = customers.filter((customer) => {
      return customer?.has_active_contracts !== 2;
    });
    setInactivesCustomersState(customers);
  };

  const getProspects = async () => {
    const route = "customers/read/hasActiveContracts";
    const prospectsActive = await getCustomerByParameter(route, {
      status: 2,
      isActive: 1,
    });
    const prospectsInactive = await getCustomerByParameter(route, {
      status: 2,
      isActive: 0,
    });
    const allProspects = prospectsActive.concat(prospectsInactive);
    setProspectsState(allProspects);
  };

  const formatAllCustomers = () => {
    let allCustomers = activeCustomersWithActiveContract
      .concat(inactiveCustomers)
      .concat(prospects)
      .concat(activeCustomersWithInactiveContract);

    setAllCustomersState(allCustomers);
  };

  const getCurrentUserAccesses = async () => {
    const data = await dynamoSwitchRoleAccessesPerUser();
    setUserAccesses(data);
  };

  useEffect(() => {
    getActiveCustomersWithActiveContract();
    getActiveCustomersWithInactiveContract();
    getInactiveCustomers();
    getProspects();
    getCurrentUserAccesses();
    contractsController.getWallets();
  }, []);

  useEffect(() => {
    formatAllCustomers();
  }, [
    activeCustomersWithActiveContract,
    activeCustomersWithInactiveContract,
    inactiveCustomers,
    prospects,
  ]);

  const tableData = useMemo(() => {
    let filteredData =
      state === "ativosA"
        ? activeCustomersWithActiveContract
        : state === "ativosI"
        ? activeCustomersWithInactiveContract
        : state === "inativosC"
        ? inactiveCustomers
        : state === "prospect"
        ? prospects
        : allCustomers;

    // Verificação de segurança - garantir que filteredData é um array
    if (!Array.isArray(filteredData)) {
      console.warn("⚠️ Clients: filteredData não é um array:", filteredData);
      filteredData = [];
    }

    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    // Verificação de segurança após filterBySearch
    if (!Array.isArray(filteredData)) {
      console.warn("⚠️ Clients: filteredData após filterBySearch não é um array:", filteredData);
      filteredData = [];
    }

    filteredData = filteredData.filter((data) => {
      const customerCreation = moment(data?.created_at).date(15);

      if (dtEnd !== null && dtStart !== null) {
        return (
          customerCreation >= dtStart.date(1) &&
          customerCreation <= dtEnd.date(28)
        );
      }
      return true;
    });

    return filteredData;
  }, [
    search,
    state,
    activeCustomersWithActiveContract,
    activeCustomersWithInactiveContract,
    inactiveCustomers,
    prospects,
    allCustomers,
    dtEnd,
    dtStart,
  ]);

  const changeActive = async (client) => {
    setLoading(true);
    setActiveCustomersWithActiveContractState([]);
    setActiveCustomersWithInactiveContractState([]);
    setInactivesCustomersState([]);
    setProspectsState([]);
    setAllCustomersState([]);
    try {
      try {
        await otrsPut("update/customer/" + client.identifications.itsm_id, {
          name: client?.names?.fantasy_name,
          razaosocial: client?.names?.name,
          street: `${client?.address?.tipo_logradouro} ${
            client?.address?.logradouro
          }, ${client?.address?.bairro}${
            client?.address?.complemento
              ? " " + client?.address?.complemento
              : ""
          }, ${client?.address?.numero}`,
          country: client?.address?.pais,
          city: client?.address?.cidade,
          zip: client?.address?.cep,
          cnpj: client?.cnpj,
          valid_id: client?.active === 1 ? 2 : 1,
        });
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "Ocorreu um erro ao tentar atualizar o cliente no OTRS :("
        );
      }

      try {
        await dynamoPut(`${process.env.REACT_APP_STAGE}-customers`, client.id, {
          active: client?.active ? 0 : 1,
        });
        await getActiveCustomersWithInactiveContract();
        await getInactiveCustomers();
        await getProspects();
        await getActiveCustomersWithActiveContract();
        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "O cliente foi atualizado no OTRS, mas ocorreu um erro ao tentar atualizar o cliente no DSM :("
        );
      }

      const customerName = await getCustomerName(client);
      const customerDSMID = client.dsm_id || "";
      const username = localStorage.getItem("@dsm/username");
      const title =
        client.active === 1 ? "Desativação de Cliente" : "Ativação de Cliente";
      const actionDescription = client.active === 1 ? "desativou" : "ativou";
      const description = `${username} ${actionDescription} o cliente: ${customerName} de DSM ID ${customerDSMID}`;
      try {
        logNewAuditAction(username, title, description);
      } catch (error) {
        console.log("Não foi possível adicionar o log da ", title);
        console.log({ error });
      }

      message.success(
        `Cliente${
          client.active === 1 ? " desativado" : " ativado"
        } com sucesso!`
      );
    } catch (err) {
      console.log(err);
      message.error("Ops! Ocorreu um erro ao tentar realizar esta ação...");
      setLoading(false);
    }
  };

  const columns = [
    {
      code: "view_date",
      title: "Data de Criação",
      dataIndex: "created_at",
      align: "center",
      render: (date, _) => {
        return isValid(new Date(date))
          ? format(new Date(date), "dd/MM/yyyy")
          : "Data inválida";
      },
      defaultSortOrder: "descend",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) => new Date(a?.created_at) - new Date(b?.created_at),
      width: "155px",
    },
    {
      code: "view_dsm_id",
      title: "DSM ID",
      align: "center",
      dataIndex: "dsm_id",
      sorter: (a, b) => a.dsm_id?.localeCompare(b.dsm_id),
      render: (field, item) => item.dsm_id || "-",
      sortDirections: ["descend", "ascend"],
      width: "2%",
    },
    {
      code: "view_fantasy_name",
      title: "Nome",
      dataIndex: ["names", "fantasy_name"],
      sorter: (a, b) =>
        a?.names?.fantasy_name?.localeCompare(b?.names?.fantasy_name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_cnpj",
      title: "CNPJ",
      dataIndex: "cnpj",
      key: "cnpj",
      align: "center",
    },
    {
      code: "view_tax_id",
      title: "TAX ID",
      dataIndex: "taxID",
      render: (field, item) => item.taxID || "-",
      key: "taxID",
      align: "center",
    },
    {
      code: "view_info",
      title: "Informações",
      dataIndex: "id",
      key: "id",
      width: "1%",
      align: "center",
      render: (field, item) => {
        return <VisualizeInfo currentCustomer={item} />;
      },
    },
    {
      code: "view_contacts",
      title: "Contatos",
      dataIndex: "contacts",
      key: "contacts",
      width: "1%",
      render: (field, item) => {
        return (
          <ClientUsersModal
            permissions={permissions?.data}
            contacts={field}
            clients={allCustomers}
            client={item}
            functions={[
              {
                state: "ativosA",
                function: getActiveCustomersWithActiveContract,
              },
              {
                state: "ativosI",
                function: getActiveCustomersWithInactiveContract,
              },
              { state: "inativosC", function: getInactiveCustomers },
              { state: "prospect", function: getProspects },
            ]}
            state={state}
          />
        );
      },
    },
    {
      code: "view_contracts",
      title: "Contratos",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <ClientContractsModal
            permissions={permissions}
            contractsPermissions={contractsPermissions}
            executivesData={executives}
            tables={tables}
            client={item}
          />
        );
      },
    },
    {
      code: "view_accounts",
      title: "Contas",
      dataIndex: "accounts",
      key: "accounts",
      width: "1%",
      render: (field, item) => {
        return (
          <ClientAccountsModal
            permissions={permissions?.data}
            accounts={item.accounts}
            clients={allCustomers}
            client={item}
            permissionSets={permissionSets}
            currentUserAccesses={userAccesses}
            refreshUserSolicitations={getCurrentUserAccesses}
          />
        );
      },
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <ClientEditModal
            state={state}
            functions={{
              getActiveCustomersWithActiveContract,
              getActiveCustomersWithInactiveContract,
              getInactiveCustomers,
              getProspects,
            }}
            clients={allCustomers}
            client={item}
            id={id}
          />
        );
      },
    },
    {
      code: "view_actions",
      title: "Ações",
      dataIndex: "active",
      key: "active",
      width: "1%",
      render: (active, item) => {
        return (
          <Row justify="center">
            {active === 1 ? (
              <Popconfirm
                placement="leftBottom"
                title="Tem certeza que deseja desativar este cliente?"
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  cursor: "pointer",
                  color: "black",
                }}
                onConfirm={() => changeActive(item)}
                cancelText="Cancelar"
              >
                <Button style={{ padding: "0" }} type="text">
                  <Tag color="red">Desativar</Tag>
                </Button>
              </Popconfirm>
            ) : (
              <Popconfirm
                placement="leftBottom"
                title="Tem certeza que deseja ativar este cliente?"
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  cursor: "pointer",
                  color: "black",
                }}
                onConfirm={() => changeActive(item)}
                cancelText="Cancelar"
              >
                <Button style={{ padding: "0" }} type="text">
                  <Tag color="green">Ativar</Tag>
                </Button>
              </Popconfirm>
            )}
          </Row>
        );
      },
    },
  ];

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row justify="end" style={{ marginBottom: "1em" }}>
              <Col>
                <Space>
                  {permissions?.data
                    ?.map((permission) => {
                      return permission.code;
                    })
                    .includes("create_client") ? (
                    <ClientCreateModal
                      customers={allCustomers}
                      getProspects={getProspects}
                    />
                  ) : (
                    ""
                  )}
                </Space>
              </Col>
              <Col style={{ marginLeft: "10px" }}>
                <Space>
                  {permissions?.data
                    ?.map((permission) => {
                      return permission.code;
                    })
                    .includes("view_export_report") ? (
                    <ClientExportReportModal data={tableData} />
                  ) : (
                    ""
                  )}
                </Space>
              </Col>
            </Row>
            <Row
              justify="space-between"
              gap="8px"
              style={{ marginBottom: "1em" }}
            >
              <Col md={9} flex="column">
                <Text>Pesquisar: </Text>
                <Input
                  onChange={(e) => setSearchState(e.target.value)}
                  style={{
                    minWidth: "300px",
                    width: "100%",
                    height: "34px",
                    borderRadius: "7px",
                  }}
                  value={search}
                  placeholder="Buscar cliente..."
                />
              </Col>
              <Col md={8}>
                <DateFilters />
              </Col>
              <Col md={6}>
                <Text>Filtrar por status: </Text>
                <Select
                  onChange={(e) => handleStateChange(e)}
                  defaultValue={state || "ativosA"}
                  style={{
                    width: "100%",
                    marginTop: "1px",
                  }}
                >
                  {customerSelectOptions.map((option) => {
                    return (
                      <Option key={option.value} value={option.value}>
                        {option.title}
                      </Option>
                    );
                  })}
                </Select>
              </Col>
            </Row>

            <Counter tableData={tableData} />
            <DynamicTable
              scroll={{ x: "100%" }}
              loading={
                loading === true
                  ? loading
                  : tableData.length === 0 && search === ""
                  ? true
                  : false
              }
              data={tableData}
              style={{ minWidth: "100%" }}
              columns={columns.filter((e) =>
                permissions?.data
                  ?.map((permission) => {
                    return permission.code;
                  })
                  .includes(e.code)
              )}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
