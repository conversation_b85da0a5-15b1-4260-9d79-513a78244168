import useS<PERSON> from "swr";
import { format } from "date-fns";
import React, { useState, useEffect, useMemo } from "react";
import { Button, Space, Tabs, Tag } from "antd";
import { SideMenu } from "../../components/SideMenu";
import { Layout, Card, Row, Col, Input, Typography, Select } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { CreateBilling } from "../../components/Modals/Billing/CreateBilling";
import { DetailsBilling } from "../../components/Modals/Billing/DetailsBilling";
import { EditBilling } from "../../components/Modals/Billing/EditBilling";
import { ComplianceMonitoring } from "../../components/Modals/Billing/ComplianceMonitoring";
import { CSVLink } from "react-csv";
import { v4 } from "uuid";
import {
  existsItemUncheckSort,
  initPage,
  getCurrentCustomerComplianceData,
} from "./controllers/complianceControllers";
import { Counter } from "../../components/Counter";
import { DynamicTable } from "../../components/Table/DynamicTable";
import { shallowEqual, useSelector } from "react-redux";
import { ViewTickets } from "../../components/Modals/Billing/ViewTickets";
import { ExecutivesBilling } from "../../components/Modals/Billing/ExecutivesBilling";
import { VisualizeInfo } from "../../components/Modals/Billing/VisualizeInfo";
import { SearchInput } from "../../components/SearchInput";
import { filterTableData } from "../../utils/filterTableData";
import { UsefulDocumentationBilling } from "../../components/Modals/Billing/UsefulDocumentationBilling";
import { exportAccountsReportHeaders } from "./constants/exportReportHeaders";
export const Billing = () => {
  const customers = useSelector(
    (state) => state.billing.customers,
    shallowEqual
  );
  const contracts = useSelector(
    (state) => state.billing.contracts,
    shallowEqual
  );
  const complianceItems = useSelector(
    (state) => state.billing.complianceItems,
    shallowEqual
  );

  const complianceMonitoringLoading = useSelector(
    (state) => state.billing.complianceMonitoringLoading,
    shallowEqual
  );

  const finops = useSelector((state) => state.billing.finops, shallowEqual);
  const loading = useSelector((state) => state.billing.loading, shallowEqual);
  const [collapsed, setCollapsed] = useState(false);
  const [search, setSearch] = useState("");
  const [actionsState, setActionsState] = useState("todas");
  const { Content } = Layout;
  const { Text } = Typography;
  const { Option } = Select;

  const permissions = useSWR("billing", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      return [...data.permissions.find((x) => x.page === "Billing").actions];
    } catch (error) {
      console.log('⚠️ Billing: Erro ao buscar permissões, usando fallback:', error.message);
      // Fallback com permissões básicas do Billing
      return [
        { code: "view_billing_data" },
        { code: "view_compliance" },
        { code: "view_customer_billing" },
        { code: "view_contract_billing" },
        { code: "export_billing_report" },
        { code: "view_billing_details" }
      ];
    }
  });

  useEffect(() => {
    window.scrollTo(0, 0);
    initPage(complianceItems);
  }, []);

  const filterBySearch = (data, search) => {
    let filteredData = [];

    if (data) {
      filteredData = data.filter((e) => {
        let verifyITSM,
          verifyName,
          verifyFantasyName,
          verifyDSMId,
          verifyContractName = false;
        const currentCustomer = customers.find((c) => c.id === e.id);

        if (e.dsm_id) {
          verifyDSMId = e.dsm_id.toLowerCase().includes(search.toLowerCase());
        }

        if (e.identifications.itsm_id) {
          verifyITSM = e.identifications.itsm_id
            .toString()
            .includes(search.toLowerCase());
        }

        if (e.names.name) {
          verifyName = e.names.name
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.names.fantasy_name) {
          verifyFantasyName = e.names.fantasy_name
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (Array.isArray(e.payment_percent)) {
          const currentContractId =
            e.payment_percent[e.contractIndex].contract_id;
          if (currentContractId) {
            verifyContractName = contracts
              ?.find((x) => x.id === currentContractId)
              ?.name.toLowerCase()
              .includes(search.toLowerCase());
          }
        }

        if (
          verifyITSM ||
          verifyName ||
          verifyFantasyName ||
          verifyContractName ||
          verifyDSMId
        )
          return filterByState(e, actionsState, currentCustomer);
      });
    }

    return filteredData;
  };

  const filterByState = (data, state) => {
    switch (state) {
      case "compliance": {
        if (complianceItems?.find((c) => c.customerId === data.id)?.isCompliant)
          return data;
        break;
      }

      case "não compliance": {
        if (
          complianceItems?.find((c) => c.customerId === data.id)
            ?.isCompliant === false
        )
          return data;
        break;
      }

      default:
        return data;
    }
  };

  const tableData = useMemo(() => {
    const customersWithPayment = customers?.flatMap((customer) =>
      Array.isArray(customer.payment_percent) &&
      customer.payment_percent.length > 0 &&
      customer.accounts.length > 0
        ? customer.payment_percent.map((p, index) => ({
            ...customer,
            contractIndex: index,
          }))
        : []
    );

    const filteredData = customersWithPayment?.filter((customer) => {
      return (
        Array.isArray(customer?.payment_percent) &&
        customer.payment_percent.some((p) => {
          const contractId = p.contract_id;

          if (!contractId) return false;

          const contract = contracts?.find(
            (contract) => contract.id === contractId
          );

          return (
            contract &&
            contract.active &&
            contract.name.toLowerCase().includes("billing")
          );
        })
      );
    });

    let customersWithbilling;

    if (search !== "") {
      customersWithbilling = filterBySearch(filteredData, search);
    } else {
      customersWithbilling = filteredData.filter((e) => {
        const currentCustomer = customers.find((c) => c.id === e.id);
        return filterByState(e, actionsState, currentCustomer) || null;
      });
    }

    return customersWithbilling;
  }, [customers, search, actionsState, contracts]);

  useEffect(() => {
    if (complianceItems.length > 0) return;
    getCurrentCustomerComplianceData(tableData, complianceMonitoringLoading);
  }, [tableData]);

  const pagination = {
    data: [],
  };

  const columns = [
    {
      code: "view_dsm_id",
      title: "DSM ID",
      align: "center",
      dataIndex: "dsm_id",
      defaultSortOrder: "descend",
      sorter: (a, b) => a.dsm_id?.localeCompare(b.dsm_id),
      render: (field, item) => item.dsm_id || "-",
      sortDirections: ["descend", "ascend"],
      width: "1%",
    },
    {
      code: "view_contract",
      title: "Contrato",
      key: "payment_percent",
      dataIndex: "payment_percent",
      render: (field, item) => {
        return contracts?.find(
          (x) => x.id === field[item.contractIndex]?.contract_id
        )?.name;
      },
    },
    {
      code: "view_client",
      dataIndex: "names",
      title: "Cliente",
      key: "names",
      render: (field) => field?.name || field?.fantasy_name,
      sorter: (a, b) => {
        const nameA = a?.names?.name || a?.names?.fantasy_name;
        const nameB = b?.names?.name || b?.names?.fantasy_name;
        return nameA?.localeCompare(nameB);
      },
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_creation",
      title: "Criação",
      dataIndex: "payment_percent",
      key: "payment_percent",
      sorter: (a, b) =>
        new Date(a.payment_percent[a.contractIndex].created_at) -
        new Date(b.payment_percent[b.contractIndex].created_at),
      sortDirections: ["descend", "ascend"],
      render: (field, item) =>
        format(new Date(field[item.contractIndex]?.created_at), "dd/MM/yyyy"),
      width: "1%",
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "payment_percent",
      key: "payment_percent",
      width: "1%",
      render: (field, item) => (
        <EditBilling
          contracts={contracts}
          billing={field[item.contractIndex]}
          clients={customers}
          key={v4()}
        />
      ),
    },
    {
      code: "view_details",
      title: "Invoices",
      dataIndex: "payment_percent",
      key: "payment_percent",
      width: "1%",
      render: (field, item) => {
        return (
          <DetailsBilling
            contract={contracts?.find(
              (x) => x.id === field[item.contractIndex]?.contract_id
            )}
            client={customers?.find(
              (x) => x.id === field[item.contractIndex]?.customer_id
            )}
            billing={finops?.data?.filter(
              (f) =>
                f?.identifications?.contract_id ===
                field[item.contractIndex]?.contract_id
            )}
            key={v4()}
          />
        );
      },
    },
    {
      code: "view_executives",
      title: "Executivos",
      key: "payment_percent",
      dataIndex: "payment_percent",
      render: (field, item) => {
        const executives = contracts?.find(
          (x) => x.id === field[item.contractIndex]?.contract_id
        )?.executives;
        return <ExecutivesBilling executives={executives} />;
      },
    },
    {
      code: "view_info",
      title: "Informações",
      dataIndex: "id",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Col>
              <VisualizeInfo item={item} permissions={permissions} />
            </Col>
          </Row>
        );
      },
    },
    {
      code: "view_compliance",
      title: "Compliance",
      dataIndex: "compliance_monitoring",
      key: "compliance_monitoring",
      width: "1%",
      sorter: (a, b) =>
        existsItemUncheckSort(a, "int", complianceItems, complianceItems) -
        existsItemUncheckSort(b, "int", complianceItems, complianceItems),
      sortDirections: ["descend", "ascend"],
      render: (field, item) => {
        const contract = contracts?.find(
          (x) => x.id === item.payment_percent[item.contractIndex]?.contract_id
        );

        return (
          <ComplianceTableCell
            complianceMonitoringLoading={complianceMonitoringLoading}
            complianceItems={complianceItems}
            customers={customers}
            item={item}
            contract={contract}
            permissions={permissions}
          />
        );
      },
    },
    {
      code: "view_tickets",
      title: "Tickets",
      dataIndex: "tickets",
      key: "tickets",
      width: "1%",
      render: (field, item) => {
        const contract = contracts?.find(
          (x) => x.id === item?.payment_percent[item.contractIndex]?.contract_id
        );
        return (
          <ViewTickets
            customerId={item?.identifications?.itsm_id}
            contract={contract}
          />
        );
      },
    },
  ];

  const billingTabs = [
    {
      label: "Dados de biling",
      key: "1",
      children: (
        <BillingDataTabContent
          tableData={tableData}
          loading={loading}
          columns={columns}
          permissions={permissions}
          contracts={contracts}
          customers={customers}
          complianceMonitoringLoading={complianceMonitoringLoading}
          setActionsState={setActionsState}
          setSearch={setSearch}
        />
      ),
    },
    {
      label: "Contas",
      key: "2",
      children: (
        <AccountsDataContent
          customers={customers.filter((acc) => acc?.accounts?.length > 0)}
          billingData={tableData}
        />
      ),
    },
  ];

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Tabs defaultActiveKey="1" items={billingTabs}></Tabs>
          </Card>
        </Content>
      </Layout>
      <UsefulDocumentationBilling />
    </Layout>
  );
};

const BillingFilters = (props) => {
  const { complianceMonitoringLoading, setActionsState } = props;
  const { Text } = Typography;
  const { Option } = Select;

  return (
    <>
      {complianceMonitoringLoading ? (
        <Col>
          <Space>
            <Text>Buscando dados de Compliance </Text>
            <LoadingOutlined />
          </Space>
        </Col>
      ) : (
        <Col>
          <Space>
            <Text>Filtrar por: </Text>
            <Select
              onChange={setActionsState}
              defaultValue="todas"
              style={{ width: "10rem" }}
            >
              <Option value="todas">Todas</Option>
              <Option value="compliance">Compliance</Option>
              <Option value="não compliance">Não Compliance</Option>
            </Select>
          </Space>
        </Col>
      )}
    </>
  );
};

const ComplianceTableCell = (props) => {
  const {
    complianceMonitoringLoading,
    complianceItems,
    customers,
    item,
    contract,
    permissions,
  } = props;
  return (
    <>
      {complianceMonitoringLoading ? (
        <Row justify="center">
          <LoadingOutlined />
        </Row>
      ) : (
        <ComplianceMonitoring
          customer={customers.find((x) => x.id === item.id)}
          items={complianceItems}
          key={v4()}
          contract={contract}
          permissions={permissions}
        />
      )}
    </>
  );
};

const BillingDataTabContent = (props) => {
  const {
    tableData,
    loading,
    columns,
    permissions,
    contracts,
    customers,
    complianceMonitoringLoading,
    setActionsState,
    setSearch,
  } = props;

  const pagination = {
    data: [],
  };

  return (
    <>
      <Row justify="space-between">
        <Col
          style={{
            marginBottom: "1em",
          }}
        >
          <Space>
            <Input
              style={{
                width: "300px",
                height: "35px",
                borderRadius: "7px",
              }}
              placeholder="Buscar billing..."
              onChange={(e) => setSearch(e.target.value)}
            />
            {permissions?.data
              ?.map((permission) => {
                return permission.code;
              })
              .includes("create_billing") ? (
              <CreateBilling
                contracts={contracts}
                clients={customers}
                data={customers}
                key={v4()}
              />
            ) : null}
          </Space>
        </Col>
        <BillingFilters
          complianceMonitoringLoading={complianceMonitoringLoading}
          setActionsState={setActionsState}
        />
      </Row>
      <Counter tableData={tableData} />
      <Row align="middle" justify="center">
        <Col span={24}>
          <DynamicTable
            data={tableData}
            scroll={{ x: "100%" }}
            loading={loading}
            columns={columns.filter((e) =>
              permissions?.data
                ?.map((permission) => {
                  return permission.code;
                })
                .includes(e.code)
            )}
            pagination={pagination}
          />
        </Col>
      </Row>
    </>
  );
};

const AccountsDataContent = (props) => {
  const { billingData } = props;
  const { Option } = Select;
  const [search, setSearch] = useState("");
  const [isPayer, setIsPayer] = useState("all");

  const removeDuplicates = (data) => {
    return data.filter(
      (value, index, self) =>
        self.findIndex((e) => e.account_id === value.account_id) === index
    );
  };

  const tableData = useMemo(() => {
    let formattedData = billingData || [];
    formattedData = formattedData
      .map((e) => {
        let payerAccounts = e.accounts.filter((a) => a.payer);
        return e.accounts.map((a) => {
          return {
            ...a,
            isPayer: a.payer ? "Sim" : "Não",
            customerName: e.names.name || e.names.fantasy_name,
            formatedPayerAccounts: JSON.stringify(
              payerAccounts.map((p) => p.account_id)
            ),
            payer_account_id: JSON.stringify(
              payerAccounts.map((p) => p.account_id)
            ),
          };
        });
      })
      .flat();
    formattedData = removeDuplicates(formattedData);

    switch (isPayer) {
      case "payer":
        formattedData = formattedData.filter((e) => e.payer);
        break;
      case "not-payer":
        formattedData = formattedData.filter((e) => !e.payer);
        break;
      default:
        break;
    }

    if (search !== "") {
      formattedData = filterTableData({
        searchFields: [
          "payer_account_id",
          "account_id",
          "customerName",
          "profile",
        ],
        search,
        data: formattedData,
      });
    }

    return formattedData;
  }, [billingData, search, isPayer]);

  const columns = [
    {
      code: "view_payer_account_id",
      title: "Payer Account ID",
      align: "center",
      dataIndex: "payer_account_id",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) => a.payer_account_id?.localeCompare(b.payer_account_id),
      width: "1%",
      render: (field) => {
        return JSON.parse(field).length > 0
          ? JSON.parse(field)?.map((e) => <Tag>{e}</Tag>)
          : "Sem conta Payer";
      },
    },
    {
      code: "view_dsm_id",
      title: "Account ID",
      align: "center",
      dataIndex: "account_id",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) => a.account_id?.localeCompare(b.account_id),
      width: "1%",
    },
    {
      code: "view_dsm_id",
      title: "Conta Payer",
      align: "center",
      dataIndex: "isPayer",
      sortDirections: ["descend", "ascend"],
      width: "1%",
      render: (field) => {
        return <Tag color={field === "Sim" ? "success" : ""}>{field}</Tag>;
      },
    },
    {
      code: "view_dsm_id",
      title: "Cliente",
      align: "center",
      dataIndex: "customerName",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) => a.customerName?.localeCompare(b.customerName),
      width: "1%",
    },
    {
      code: "view_dsm_id",
      title: "Nome da conta",
      align: "center",
      dataIndex: "profile",
      sortDirections: ["descend", "ascend"],
      width: "1%",
    },
  ];

  return (
    <>
      <Row justify="space-between" align="top" style={{ marginBottom: "1em" }}>
        <Col>
          <Space>
            <SearchInput
              placeholder="Buscar conta..."
              onChange={(e) => setSearch(e)}
            ></SearchInput>
            <CSVLink
              data={tableData}
              separator=";"
              filename={`contas_billing_${format(
                new Date(),
                "dd-MM-yyyy"
              )}.csv`}
              headers={exportAccountsReportHeaders}
            >
              <Button type="primary">Exportar</Button>
            </CSVLink>
          </Space>
        </Col>

        <Col>
          <Space>
            Filtrar por:
            <Select
              onChange={(e) => setIsPayer(e)}
              value={isPayer}
              style={{ width: "10em" }}
            >
              <Option value="all">Todas</Option>
              <Option value="payer">Conta Payer</Option>
              <Option value="not-payer">Conta Normal</Option>
            </Select>
          </Space>
          <Counter tableData={tableData} />
        </Col>
      </Row>
      <DynamicTable data={tableData} columns={columns} />
    </>
  );
};
